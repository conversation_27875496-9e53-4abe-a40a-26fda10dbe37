{********************************
 * Modern Arpeggiator v1.0
 * Advanced KSP Script with Modern GUI
 * Features: Step sequencer, visual feedback, multiple patterns
 ********************************}

on init
    {* Performance Settings *}
    set_script_title("Modern Arpeggiator")
    message("")
    
    {* Constants *}
    declare const $NUM_STEPS := 16
    declare const $NUM_PATTERNS := 8
    declare const $GUI_WIDTH := 630
    declare const $GUI_HEIGHT := 350
    
    {* Variables *}
    declare $current_step := 0
    declare $is_playing := 0
    declare $pattern_index := 0
    declare $last_note := 60
    declare $arp_direction := 0
    declare $octave_range := 2
    
    {* Arrays *}
    declare %step_notes[$NUM_STEPS]
    declare %step_velocity[$NUM_STEPS]
    declare %step_gate[$NUM_STEPS]
    declare %step_active[$NUM_STEPS]
    declare %led_ids[$NUM_STEPS]
    
    {* UI Variables *}
    declare ui_knob $Speed (100, 1000, 1)
    declare ui_knob $Gate (10, 100, 1)
    declare ui_knob $Swing (0, 75, 1)
    declare ui_knob $Velocity (1, 127, 1)
    declare ui_switch $Play
    declare ui_switch $Sync
    declare ui_menu $Direction
    declare ui_menu $Pattern
    declare ui_value_edit $Steps (1, 16, 1)
    declare ui_value_edit $Octaves (1, 4, 1)
    
    {* Step buttons and LEDs *}
    declare $i
    declare $j
    declare $k
    declare $wait_time
    declare $note_offset
    declare $actual_velocity
    declare $current_playing_note
    declare ui_switch $step1
    declare ui_switch $step2
    declare ui_switch $step3
    declare ui_switch $step4
    declare ui_switch $step5
    declare ui_switch $step6
    declare ui_switch $step7
    declare ui_switch $step8
    declare ui_switch $step9
    declare ui_switch $step10
    declare ui_switch $step11
    declare ui_switch $step12
    declare ui_switch $step13
    declare ui_switch $step14
    declare ui_switch $step15
    declare ui_switch $step16
    
    {* Initialize GUI *}
    make_perfview
    set_ui_height_px(350)
    
    {* Main Panel Background *}
    set_control_par_str($INST_WALLPAPER_ID, $CONTROL_PAR_PICTURE, "")
    
    {* Position Controls *}
    move_control_px($Speed, 20, 40)
    move_control_px($Gate, 110, 40)
    move_control_px($Swing, 200, 40)
    move_control_px($Velocity, 290, 40)
    
    move_control_px($Play, 400, 50)
    move_control_px($Sync, 480, 50)
    
    move_control_px($Direction, 20, 140)
    move_control_px($Pattern, 150, 140)
    move_control_px($Steps, 280, 140)
    move_control_px($Octaves, 380, 140)
    
    {* Style Controls *}
    set_control_par($Speed, $CONTROL_PAR_WIDTH, 80)
    set_control_par($Speed, $CONTROL_PAR_HEIGHT, 80)
    set_text($Speed, "Speed")
    set_control_par_str($Speed, $CONTROL_PAR_LABEL, "")
    
    set_control_par($Gate, $CONTROL_PAR_WIDTH, 80)
    set_control_par($Gate, $CONTROL_PAR_HEIGHT, 80)
    set_text($Gate, "Gate")
    set_control_par_str($Gate, $CONTROL_PAR_LABEL, "")
    
    set_control_par($Swing, $CONTROL_PAR_WIDTH, 80)
    set_control_par($Swing, $CONTROL_PAR_HEIGHT, 80)
    set_text($Swing, "Swing")
    set_control_par_str($Swing, $CONTROL_PAR_LABEL, "")
    
    set_control_par($Velocity, $CONTROL_PAR_WIDTH, 80)
    set_control_par($Velocity, $CONTROL_PAR_HEIGHT, 80)
    set_text($Velocity, "Velocity")
    set_control_par_str($Velocity, $CONTROL_PAR_LABEL, "")
    
    {* Style switches *}
    set_control_par($Play, $CONTROL_PAR_WIDTH, 60)
    set_control_par($Play, $CONTROL_PAR_HEIGHT, 30)
    set_text($Play, "PLAY")
    
    set_control_par($Sync, $CONTROL_PAR_WIDTH, 60)
    set_control_par($Sync, $CONTROL_PAR_HEIGHT, 30)
    set_text($Sync, "SYNC")
    
    {* Setup menus *}
    set_control_par($Direction, $CONTROL_PAR_WIDTH, 120)
    add_menu_item($Direction, "Up", 0)
    add_menu_item($Direction, "Down", 1)
    add_menu_item($Direction, "Up/Down", 2)
    add_menu_item($Direction, "Random", 3)
    
    set_control_par($Pattern, $CONTROL_PAR_WIDTH, 120)
    $i := 0
    while ($i < $NUM_PATTERNS)
        add_menu_item($Pattern, "Pattern " & $i+1, $i)
        inc($i)
    end while
    
    {* Position step buttons *}
    move_control_px($step1, 20, 220)
    move_control_px($step2, 58, 220)
    move_control_px($step3, 96, 220)
    move_control_px($step4, 134, 220)
    move_control_px($step5, 172, 220)
    move_control_px($step6, 210, 220)
    move_control_px($step7, 248, 220)
    move_control_px($step8, 286, 220)
    move_control_px($step9, 324, 220)
    move_control_px($step10, 362, 220)
    move_control_px($step11, 400, 220)
    move_control_px($step12, 438, 220)
    move_control_px($step13, 476, 220)
    move_control_px($step14, 514, 220)
    move_control_px($step15, 552, 220)
    move_control_px($step16, 590, 220)
    
    {* Style step buttons *}
    set_control_par($step1, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step1, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step2, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step2, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step3, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step3, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step4, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step4, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step5, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step5, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step6, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step6, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step7, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step7, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step8, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step8, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step9, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step9, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step10, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step10, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step11, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step11, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step12, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step12, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step13, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step13, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step14, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step14, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step15, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step15, $CONTROL_PAR_HEIGHT, 60)
    set_control_par($step16, $CONTROL_PAR_WIDTH, 35)
    set_control_par($step16, $CONTROL_PAR_HEIGHT, 60)
    
    {* Initialize arrays *}
    $i := 0
    while ($i < $NUM_STEPS)
        %step_active[$i] := 1
        %step_notes[$i] := 0
        %step_velocity[$i] := 100
        %step_gate[$i] := 80
        inc($i)
    end while
    
    {* Set default values *}
    $Speed := 500
    $Gate := 80
    $Swing := 0
    $Velocity := 100
    $Steps := 16
    $Octaves := 2
    $Play := 0
    $Sync := 1
    $current_playing_note := 0
    
    {* Initialize first pattern *}
    %step_active[0] := 1
    %step_active[4] := 1
    %step_active[8] := 1
    %step_active[12] := 1
    
    $step1 := 1
    $step5 := 1
    $step9 := 1
    $step13 := 1
    
    {* Display labels *}
    declare ui_label $title_label (1, 1)
    set_text($title_label, "MODERN ARPEGGIATOR")
    move_control_px($title_label, 250, 10)
    set_control_par($title_label, $CONTROL_PAR_WIDTH, 200)
    set_control_par($title_label, $CONTROL_PAR_TEXT_ALIGNMENT, 1)
    
    declare ui_label $pattern_label (1, 1)
    set_text($pattern_label, "STEP SEQUENCER")
    move_control_px($pattern_label, 250, 190)
    set_control_par($pattern_label, $CONTROL_PAR_WIDTH, 200)
    set_control_par($pattern_label, $CONTROL_PAR_TEXT_ALIGNMENT, 1)
    
    declare ui_label $info_label (1, 1)
    set_text($info_label, "Ready")
    move_control_px($info_label, 20, 300)
    set_control_par($info_label, $CONTROL_PAR_WIDTH, 600)
    set_control_par($info_label, $CONTROL_PAR_HEIGHT, 20)
    
end on

{* Note handling *}
on note
    if ($is_playing = 0 and $Play = 1)
        $last_note := $EVENT_NOTE
        $is_playing := 1
        $current_step := 0
        set_text($info_label, "Playing pattern from note " & $EVENT_NOTE)
    end if
end on

on release
    if ($is_playing = 1)
        {* Stop any playing note *}
        if ($current_playing_note > 0)
            set_midi($MIDI_COMMAND_NOTE_OFF, 0, $current_playing_note, 0)
            $current_playing_note := 0
        end if
        $is_playing := 0
        $Play := 0
        set_text($info_label, "Stopped")
    end if
end on

{* Timer callback for sequencer *}
on listener
    if ($is_playing = 1 and $Play = 1)
        {* Calculate note based on direction *}
        select ($Direction)
            case 0 {* Up *}
                $note_offset := ($current_step mod 4) * 12
            case 1 {* Down *}
                $note_offset := (3 - ($current_step mod 4)) * 12
            case 2 {* Up/Down *}
                if ($current_step < 8)
                    $note_offset := ($current_step mod 4) * 12
                else
                    $note_offset := (3 - ($current_step mod 4)) * 12
                end if
            case 3 {* Random *}
                $note_offset := random(0, 3) * 12
        end select
        
        {* Stop previous note if playing *}
        if ($current_playing_note > 0)
            set_midi($MIDI_COMMAND_NOTE_OFF, 0, $current_playing_note, 0)
            $current_playing_note := 0
        end if

        {* Play note if step is active *}
        if (%step_active[$current_step] = 1)
            $actual_velocity := ($Velocity * %step_velocity[$current_step]) / 127
            $current_playing_note := $last_note + $note_offset
            {* Send MIDI note instead of play_note for multi script compatibility *}
            set_midi($MIDI_COMMAND_NOTE_ON, 0, $current_playing_note, $actual_velocity)
        end if
        
        {* Update step LEDs - inline code instead of function call *}
        {* Reset all step colors *}
        set_control_par($step1, $CONTROL_PAR_VALUE, %step_active[0])
        set_control_par($step2, $CONTROL_PAR_VALUE, %step_active[1])
        set_control_par($step3, $CONTROL_PAR_VALUE, %step_active[2])
        set_control_par($step4, $CONTROL_PAR_VALUE, %step_active[3])
        set_control_par($step5, $CONTROL_PAR_VALUE, %step_active[4])
        set_control_par($step6, $CONTROL_PAR_VALUE, %step_active[5])
        set_control_par($step7, $CONTROL_PAR_VALUE, %step_active[6])
        set_control_par($step8, $CONTROL_PAR_VALUE, %step_active[7])
        set_control_par($step9, $CONTROL_PAR_VALUE, %step_active[8])
        set_control_par($step10, $CONTROL_PAR_VALUE, %step_active[9])
        set_control_par($step11, $CONTROL_PAR_VALUE, %step_active[10])
        set_control_par($step12, $CONTROL_PAR_VALUE, %step_active[11])
        set_control_par($step13, $CONTROL_PAR_VALUE, %step_active[12])
        set_control_par($step14, $CONTROL_PAR_VALUE, %step_active[13])
        set_control_par($step15, $CONTROL_PAR_VALUE, %step_active[14])
        set_control_par($step16, $CONTROL_PAR_VALUE, %step_active[15])
        
        {* Highlight current step *}
        select ($current_step)
            case 0
                set_control_par($step1, $CONTROL_PAR_VALUE, 1)
            case 1
                set_control_par($step2, $CONTROL_PAR_VALUE, 1)
            case 2
                set_control_par($step3, $CONTROL_PAR_VALUE, 1)
            case 3
                set_control_par($step4, $CONTROL_PAR_VALUE, 1)
            case 4
                set_control_par($step5, $CONTROL_PAR_VALUE, 1)
            case 5
                set_control_par($step6, $CONTROL_PAR_VALUE, 1)
            case 6
                set_control_par($step7, $CONTROL_PAR_VALUE, 1)
            case 7
                set_control_par($step8, $CONTROL_PAR_VALUE, 1)
            case 8
                set_control_par($step9, $CONTROL_PAR_VALUE, 1)
            case 9
                set_control_par($step10, $CONTROL_PAR_VALUE, 1)
            case 10
                set_control_par($step11, $CONTROL_PAR_VALUE, 1)
            case 11
                set_control_par($step12, $CONTROL_PAR_VALUE, 1)
            case 12
                set_control_par($step13, $CONTROL_PAR_VALUE, 1)
            case 13
                set_control_par($step14, $CONTROL_PAR_VALUE, 1)
            case 14
                set_control_par($step15, $CONTROL_PAR_VALUE, 1)
            case 15
                set_control_par($step16, $CONTROL_PAR_VALUE, 1)
        end select
        
        {* Move to next step *}
        inc($current_step)
        if ($current_step >= $Steps)
            $current_step := 0
        end if
        
        {* Calculate timing with swing *}
        if ($current_step mod 2 = 0)
            $wait_time := $Speed + (($Speed * $Swing) / 100)
        else
            $wait_time := $Speed - (($Speed * $Swing) / 100)
        end if
        
        wait($wait_time * 1000)
    end if
end on

{* Control callbacks *}
on ui_control ($Play)
    if ($Play = 1)
        set_text($info_label, "Waiting for note...")
    else
        {* Stop any playing note *}
        if ($current_playing_note > 0)
            set_midi($MIDI_COMMAND_NOTE_OFF, 0, $current_playing_note, 0)
            $current_playing_note := 0
        end if
        $is_playing := 0
        set_text($info_label, "Stopped")
    end if
end on

on ui_control ($step1)
    %step_active[0] := $step1
end on

on ui_control ($step2)
    %step_active[1] := $step2
end on

on ui_control ($step3)
    %step_active[2] := $step3
end on

on ui_control ($step4)
    %step_active[3] := $step4
end on

on ui_control ($step5)
    %step_active[4] := $step5
end on

on ui_control ($step6)
    %step_active[5] := $step6
end on

on ui_control ($step7)
    %step_active[6] := $step7
end on

on ui_control ($step8)
    %step_active[7] := $step8
end on

on ui_control ($step9)
    %step_active[8] := $step9
end on

on ui_control ($step10)
    %step_active[9] := $step10
end on

on ui_control ($step11)
    %step_active[10] := $step11
end on

on ui_control ($step12)
    %step_active[11] := $step12
end on

on ui_control ($step13)
    %step_active[12] := $step13
end on

on ui_control ($step14)
    %step_active[13] := $step14
end on

on ui_control ($step15)
    %step_active[14] := $step15
end on

on ui_control ($step16)
    %step_active[15] := $step16
end on

{* Pattern management *}
on ui_control ($Pattern)
    {* Reset pattern first *}
    $k := 0
    while ($k < $NUM_STEPS)
        %step_active[$k] := 0
        inc($k)
    end while
    
    select ($Pattern)
        case 0 {* Basic 4/4 *}
            %step_active[0] := 1
            %step_active[4] := 1
            %step_active[8] := 1
            %step_active[12] := 1
        case 1 {* Syncopated *}
            %step_active[0] := 1
            %step_active[3] := 1
            %step_active[6] := 1
            %step_active[10] := 1
            %step_active[13] := 1
        case 2 {* Dense *}
            %step_active[0] := 1
            %step_active[2] := 1
            %step_active[4] := 1
            %step_active[6] := 1
            %step_active[8] := 1
            %step_active[10] := 1
            %step_active[12] := 1
            %step_active[14] := 1
        case 3 {* Triplets *}
            %step_active[0] := 1
            %step_active[3] := 1
            %step_active[6] := 1
            %step_active[9] := 1
            %step_active[12] := 1
            %step_active[15] := 1
        case 4 {* Random *}
            $j := 0
            while ($j < $NUM_STEPS)
                if (random(0, 100) > 50)
                    %step_active[$j] := 1
                end if
                inc($j)
            end while
    end select
    
    {* Update UI *}
    $step1 := %step_active[0]
    $step2 := %step_active[1]
    $step3 := %step_active[2]
    $step4 := %step_active[3]
    $step5 := %step_active[4]
    $step6 := %step_active[5]
    $step7 := %step_active[6]
    $step8 := %step_active[7]
    $step9 := %step_active[8]
    $step10 := %step_active[9]
    $step11 := %step_active[10]
    $step12 := %step_active[11]
    $step13 := %step_active[12]
    $step14 := %step_active[13]
    $step15 := %step_active[14]
    $step16 := %step_active[15]
    
    set_text($info_label, "Loaded " & get_menu_item_str($Pattern, $Pattern))
end on